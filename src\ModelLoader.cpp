#include "ModelLoader.h"
#include <iostream>
#include <algorithm>
#include <cmath>

// Definir se Assimp está disponível
#ifdef ASSIMP_AVAILABLE
#include <assimp/Importer.hpp>
#include <assimp/scene.h>
#include <assimp/postprocess.h>
#endif

// Implementação da estrutura Model3D
Model3D::Model3D() : boundingBoxMin(0, 0, 0), boundingBoxMax(0, 0, 0), 
                     center(0, 0, 0), radius(0) {}

void Model3D::calculateBounds() {
    if (meshes.empty()) return;
    
    bool first = true;
    for (const auto& mesh : meshes) {
        for (const auto& vertex : mesh.vertices) {
            if (first) {
                boundingBoxMin = boundingBoxMax = vertex.position;
                first = false;
            } else {
                boundingBoxMin.x = std::min(boundingBoxMin.x, vertex.position.x);
                boundingBoxMin.y = std::min(boundingBoxMin.y, vertex.position.y);
                boundingBoxMin.z = std::min(boundingBoxMin.z, vertex.position.z);
                boundingBoxMax.x = std::max(boundingBoxMax.x, vertex.position.x);
                boundingBoxMax.y = std::max(boundingBoxMax.y, vertex.position.y);
                boundingBoxMax.z = std::max(boundingBoxMax.z, vertex.position.z);
            }
        }
    }
    
    center = (boundingBoxMin + boundingBoxMax) * 0.5f;
    Vector3 size = boundingBoxMax - boundingBoxMin;
    radius = std::max({size.x, size.y, size.z}) * 0.5f;
}

void Model3D::render(bool wireframe) {
    for (auto& mesh : meshes) {
        mesh.render(wireframe);
    }
}

void Model3D::normalize(float targetRadius) {
    calculateBounds();
    if (radius == 0) return;
    
    float scale = targetRadius / radius;
    for (auto& mesh : meshes) {
        for (auto& vertex : mesh.vertices) {
            vertex.position = (vertex.position - center) * scale;
        }
        mesh.calculateBounds();
    }
    
    calculateBounds();
}

// Implementação do ModelLoader
bool ModelLoader::isAssimpAvailable() {
#ifdef ASSIMP_AVAILABLE
    return true;
#else
    return false;
#endif
}

std::vector<std::string> ModelLoader::getSupportedFormats() {
    return {"fbx", "obj", "dae", "3ds", "blend", "ply", "stl", "x", "md5mesh"};
}

std::string ModelLoader::getFileExtension(const std::string& filename) {
    size_t pos = filename.find_last_of('.');
    if (pos != std::string::npos) {
        std::string ext = filename.substr(pos + 1);
        std::transform(ext.begin(), ext.end(), ext.begin(), ::tolower);
        return ext;
    }
    return "";
}

bool ModelLoader::isFormatSupported(const std::string& filename) {
    std::string ext = getFileExtension(filename);
    auto formats = getSupportedFormats();
    return std::find(formats.begin(), formats.end(), ext) != formats.end();
}

Model3D ModelLoader::createFallbackModel() {
    std::cout << "ModelLoader: Criando modelo fallback (cubo)" << std::endl;
    Model3D model;
    model.meshes.push_back(Mesh::createCube());
    model.materials.push_back(Material());
    model.calculateBounds();
    return model;
}

Model3D ModelLoader::loadModel(const std::string& filename) {
    std::cout << "ModelLoader: Tentando carregar modelo: " << filename << std::endl;
    
    if (!isFormatSupported(filename)) {
        std::cout << "ModelLoader: Formato não suportado: " << getFileExtension(filename) << std::endl;
        return createFallbackModel();
    }
    
#ifdef ASSIMP_AVAILABLE
    return loadWithAssimp(filename);
#else
    std::cout << "ModelLoader: Assimp não disponível. Usando modelo fallback." << std::endl;
    return createFallbackModel();
#endif
}

#ifdef ASSIMP_AVAILABLE
Model3D ModelLoader::loadWithAssimp(const std::string& filename) {
    Assimp::Importer importer;
    
    const aiScene* scene = importer.ReadFile(filename, 
        aiProcess_Triangulate | 
        aiProcess_FlipUVs | 
        aiProcess_CalcTangentSpace |
        aiProcess_GenNormals);
    
    if (!scene || scene->mFlags & AI_SCENE_FLAGS_INCOMPLETE || !scene->mRootNode) {
        std::cout << "ModelLoader: Erro ao carregar com Assimp: " << importer.GetErrorString() << std::endl;
        return createFallbackModel();
    }
    
    Model3D model;
    
    // Processa todas as meshes
    for (unsigned int i = 0; i < scene->mNumMeshes; i++) {
        model.meshes.push_back(processMesh(scene->mMeshes[i], scene));
    }
    
    // Processa materiais
    for (unsigned int i = 0; i < scene->mNumMaterials; i++) {
        model.materials.push_back(processMaterial(scene->mMaterials[i]));
    }
    
    model.calculateBounds();
    std::cout << "ModelLoader: Modelo carregado com sucesso! Meshes: " << model.meshes.size() 
              << ", Materiais: " << model.materials.size() << std::endl;
    
    return model;
}

Mesh ModelLoader::processMesh(void* meshPtr, void* scenePtr) {
    aiMesh* mesh = static_cast<aiMesh*>(meshPtr);
    const aiScene* scene = static_cast<const aiScene*>(scenePtr);
    
    Mesh result;
    
    // Processa vértices
    for (unsigned int i = 0; i < mesh->mNumVertices; i++) {
        Vertex vertex;
        
        // Posição
        vertex.position = Vector3(mesh->mVertices[i].x, mesh->mVertices[i].y, mesh->mVertices[i].z);
        
        // Normal
        if (mesh->mNormals) {
            vertex.normal = Vector3(mesh->mNormals[i].x, mesh->mNormals[i].y, mesh->mNormals[i].z);
        }
        
        // Coordenadas de textura
        if (mesh->mTextureCoords[0]) {
            vertex.u = mesh->mTextureCoords[0][i].x;
            vertex.v = mesh->mTextureCoords[0][i].y;
        } else {
            vertex.u = vertex.v = 0.0f;
        }
        
        result.vertices.push_back(vertex);
    }
    
    // Processa faces
    for (unsigned int i = 0; i < mesh->mNumFaces; i++) {
        aiFace face = mesh->mFaces[i];
        if (face.mNumIndices == 3) {
            result.addTriangle(face.mIndices[0], face.mIndices[1], face.mIndices[2]);
        }
    }
    
    result.calculateBounds();
    return result;
}

Material ModelLoader::processMaterial(void* materialPtr) {
    aiMaterial* mat = static_cast<aiMaterial*>(materialPtr);
    Material material;
    
    aiColor3D color(0.f, 0.f, 0.f);
    
    if (mat->Get(AI_MATKEY_COLOR_AMBIENT, color) == AI_SUCCESS) {
        material.ambient = Vector3(color.r, color.g, color.b);
    }
    
    if (mat->Get(AI_MATKEY_COLOR_DIFFUSE, color) == AI_SUCCESS) {
        material.diffuse = Vector3(color.r, color.g, color.b);
    }
    
    if (mat->Get(AI_MATKEY_COLOR_SPECULAR, color) == AI_SUCCESS) {
        material.specular = Vector3(color.r, color.g, color.b);
    }
    
    float shininess;
    if (mat->Get(AI_MATKEY_SHININESS, shininess) == AI_SUCCESS) {
        material.shininess = shininess;
    }
    
    // Textura difusa
    aiString texPath;
    if (mat->GetTexture(aiTextureType_DIFFUSE, 0, &texPath) == AI_SUCCESS) {
        material.diffuseTexture = texPath.C_Str();
    }
    
    return material;
}
#else
// Implementações vazias quando Assimp não está disponível
Model3D ModelLoader::loadWithAssimp(const std::string& filename) {
    return createFallbackModel();
}

Mesh ModelLoader::processMesh(void* mesh, void* scene) {
    return Mesh::createCube();
}

Material ModelLoader::processMaterial(void* material) {
    return Material();
}
#endif
