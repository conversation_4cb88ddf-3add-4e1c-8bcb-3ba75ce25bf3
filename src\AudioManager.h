#ifndef AUDIOMANAGER_H
#define AUDIOMANAGER_H

#include <string>

#ifdef _WIN32
    #include <windows.h>
    #include <mmsystem.h>
    // Para MinGW, a biblioteca será linkada via projeto

    // Thread para controle de áudio
    #include <thread>
#endif

class AudioManager {
private:
    bool initialized;
    bool musicPlaying;
    bool musicPaused;
    std::string currentMusicFile;
    float volume;

public:
    AudioManager();
    ~AudioManager();

    // Inicialização e limpeza
    bool initialize();
    void cleanup();

    // Controle de música
    bool loadMusic(const std::string& filename);
    void playMusic(bool loop = true);
    void pauseMusic();
    void resumeMusic();
    void stopMusic();

    // Controle de volume
    void setVolume(float vol); // 0.0f a 1.0f
    float getVolume() const { return volume; }

    // Estado
    bool isMusicPlaying() const { return musicPlaying && !musicPaused; }
    bool isMusicPaused() const { return musicPaused; }
    bool isInitialized() const { return initialized; }

    // Efeitos sonoros (para futuro uso)
    void playSound(const std::string& filename);

private:
    void updateMusicState();

#ifdef _WIN32
    std::string convertToWideString(const std::string& str);
#endif
};

#endif // AUDIOMANAGER_H
