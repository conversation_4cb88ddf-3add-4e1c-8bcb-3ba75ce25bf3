# Modelos 3D - Simulador de Asteroides

Esta pasta é destinada aos modelos 3D que podem ser importados no simulador.

## Formatos Suportados

O sistema suporta os seguintes formatos de modelo 3D:
- **FBX** (recomendado)
- **OBJ** 
- **DAE** (Collada)
- **3DS**
- **BLEND** (Blender)
- **PLY**
- **STL**
- **X** (DirectX)
- **MD5MESH**

## Como Usar

### 1. Colocando Modelos
- Coloque seus arquivos de modelo 3D nesta pasta (`models/`)
- Certifique-se de que o arquivo tenha uma das extensões suportadas

### 2. Carregando no Simulador
- Execute o simulador
- Pressione **O** para tentar carregar um modelo de exemplo
- O sistema tentará carregar automaticamente os seguintes arquivos:
  - `example.fbx`
  - `example.obj`
  - `test.fbx`
  - `test.obj`
  - `model.fbx`
  - `model.obj`
  - `cube.fbx`
  - `cube.obj`

### 3. Comandos no Simulador
- **I**: Lista modelos carregados e instâncias criadas
- **O**: Carrega modelo de exemplo e cria uma instância próxima ao jogador

## Observações Importantes

### Biblioteca Assimp
- O sistema usa a biblioteca **Assimp** para carregar modelos 3D
- Se Assimp não estiver disponível, o sistema criará um cubo como fallback
- Para compilar com suporte completo, instale Assimp:
  ```bash
  # Windows (vcpkg)
  vcpkg install assimp
  
  # Linux
  sudo apt-get install libassimp-dev
  
  # macOS
  brew install assimp
  ```

### Sem Assimp
- Se Assimp não estiver disponível, o sistema ainda funcionará
- Modelos serão substituídos por cubos procedurais
- Todas as funcionalidades básicas continuam operacionais

### Texturas
- Texturas referenciadas nos modelos devem estar na mesma pasta
- O sistema tentará carregar texturas automaticamente
- Se texturas não forem encontradas, serão usadas texturas procedurais

## Exemplos de Uso

### Modelo Simples
1. Baixe um modelo .fbx ou .obj
2. Renomeie para `example.fbx` ou `example.obj`
3. Coloque nesta pasta
4. Execute o simulador e pressione **O**

### Múltiplos Modelos
- O sistema pode carregar múltiplos modelos
- Cada modelo recebe um nome único
- Múltiplas instâncias podem ser criadas do mesmo modelo

## Resolução de Problemas

### Modelo não carrega
- Verifique se o arquivo está nesta pasta
- Confirme que a extensão é suportada
- Verifique se o arquivo não está corrompido

### Modelo aparece muito grande/pequeno
- O sistema normaliza automaticamente os modelos
- Modelos são ajustados para um tamanho padrão

### Modelo aparece sem cor
- Verifique se as texturas estão na mesma pasta
- O sistema usará cores procedurais se texturas não forem encontradas

## Estrutura do Sistema

```
models/                 <- Esta pasta
├── README.md          <- Este arquivo
├── example.fbx        <- Seu modelo aqui
├── texture.jpg        <- Texturas do modelo
└── ...
```

## Status Atual

- ✅ Carregamento básico de modelos
- ✅ Fallback para modelos procedurais
- ✅ Sistema de instâncias
- ✅ Renderização wireframe/sólida
- ⚠️ Assimp opcional (funciona sem)
- 🔄 Texturas em desenvolvimento
- 🔄 Animações não implementadas

Para mais informações, consulte o código fonte em `src/ModelLoader.cpp` e `src/Model3DManager.cpp`.
