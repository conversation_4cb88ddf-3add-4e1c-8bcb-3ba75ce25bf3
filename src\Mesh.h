#ifndef MESH_H
#define MESH_H

#include "Vector3.h"
#include <vector>
#include <string>
#include <GL/glut.h>

struct Vertex {
    Vector3 position;
    Vector3 normal;
    float u, v;

    Vertex() {}
    Vertex(const Vector3& pos) : position(pos) {}
    Vertex(const Vector3& pos, const Vector3& norm) : position(pos), normal(norm) {}
};



struct Triangle {
    unsigned int indices[3];
    Vector3 normal;

    Triangle(unsigned int a, unsigned int b, unsigned int c) {
        indices[0] = a;
        indices[1] = b;
        indices[2] = c;
    }
};

class Mesh {
public:
    std::vector<Vertex> vertices;
    std::vector<Triangle> triangles;
    Vector3 center;
    float radius;

    Mesh();
    ~Mesh();

    void clear();
    void addVertex(const Vector3& position);
    void addTriangle(unsigned int a, unsigned int b, unsigned int c);

    void calculateNormals();
    void calculateTexCoords();
    void calculateBounds();
    void normalize(float targetRadius = 1.0f);

    void render(bool wireframe = false);
    void renderImmediate(bool wireframe = false);

    void subdivide();
    void smooth(float factor = 0.5f);
    void displace(float amplitude, int octaves = 3);

    static Mesh createIcosphere(int subdivisions = 2);
    static Mesh createCube();
    static Mesh createSphere(int segments = 16);

    // Carregamento de modelos 3D
    static Mesh loadFromFile(const std::string& filename);
    static bool isModelFormatSupported(const std::string& filename);

private:
    void calculateTriangleNormal(Triangle& tri);
    float noise3D(float x, float y, float z, int octave = 0);
    Vector3 getVertexOnUnitSphere(const Vector3& vertex);
};

#endif
