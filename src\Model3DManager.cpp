#include "Model3DManager.h"
#include <iostream>
#include <algorithm>
#include <GL/glut.h>

Model3DManager::Model3DManager() : modelsDirectory("models/") {
    std::cout << "Model3DManager: Inicializado" << std::endl;
}

Model3DManager::~Model3DManager() {
    unloadAllModels();
}

void Model3DManager::setModelsDirectory(const std::string& directory) {
    modelsDirectory = directory;
    if (!modelsDirectory.empty() && modelsDirectory.back() != '/') {
        modelsDirectory += '/';
    }
    std::cout << "Model3DManager: Diretório de modelos definido como: " << modelsDirectory << std::endl;
}

bool Model3DManager::loadModel(const std::string& name, const std::string& filename) {
    if (isModelLoaded(name)) {
        std::cout << "Model3DManager: Modelo '" << name << "' j<PERSON> está carregado" << std::endl;
        return true;
    }
    
    std::string fullPath = modelsDirectory + filename;
    std::cout << "Model3DManager: Carregando modelo '" << name << "' de: " << fullPath << std::endl;
    
    Model3D model = ModelLoader::loadModel(fullPath);
    
    if (model.meshes.empty()) {
        std::cout << "Model3DManager: Falha ao carregar modelo '" << name << "'" << std::endl;
        return false;
    }
    
    loadedModels[name] = model;
    std::cout << "Model3DManager: Modelo '" << name << "' carregado com sucesso!" << std::endl;
    std::cout << "  - Meshes: " << model.meshes.size() << std::endl;
    std::cout << "  - Materiais: " << model.materials.size() << std::endl;
    std::cout << "  - Raio: " << model.radius << std::endl;
    
    return true;
}

bool Model3DManager::isModelLoaded(const std::string& name) const {
    return loadedModels.find(name) != loadedModels.end();
}

void Model3DManager::unloadModel(const std::string& name) {
    auto it = loadedModels.find(name);
    if (it != loadedModels.end()) {
        loadedModels.erase(it);
        std::cout << "Model3DManager: Modelo '" << name << "' descarregado" << std::endl;
        
        // Remove instâncias que usam este modelo
        instances.erase(
            std::remove_if(instances.begin(), instances.end(),
                [&name](const Model3DInstance& inst) { return inst.modelName == name; }),
            instances.end()
        );
    }
}

void Model3DManager::unloadAllModels() {
    loadedModels.clear();
    instances.clear();
    std::cout << "Model3DManager: Todos os modelos descarregados" << std::endl;
}

int Model3DManager::createInstance(const std::string& modelName, const Vector3& position) {
    if (!isModelLoaded(modelName)) {
        std::cout << "Model3DManager: Tentativa de criar instância de modelo não carregado: " << modelName << std::endl;
        return -1;
    }
    
    Model3DInstance instance(modelName, position);
    instances.push_back(instance);
    int instanceId = instances.size() - 1;
    
    std::cout << "Model3DManager: Instância criada - ID: " << instanceId 
              << ", Modelo: " << modelName << ", Posição: (" 
              << position.x << ", " << position.y << ", " << position.z << ")" << std::endl;
    
    return instanceId;
}

void Model3DManager::removeInstance(int instanceId) {
    if (isValidInstanceId(instanceId)) {
        instances.erase(instances.begin() + instanceId);
        std::cout << "Model3DManager: Instância " << instanceId << " removida" << std::endl;
    }
}

void Model3DManager::setInstancePosition(int instanceId, const Vector3& position) {
    if (isValidInstanceId(instanceId)) {
        instances[instanceId].position = position;
    }
}

void Model3DManager::setInstanceRotation(int instanceId, const Vector3& rotation) {
    if (isValidInstanceId(instanceId)) {
        instances[instanceId].rotation = rotation;
    }
}

void Model3DManager::setInstanceScale(int instanceId, const Vector3& scale) {
    if (isValidInstanceId(instanceId)) {
        instances[instanceId].scale = scale;
    }
}

void Model3DManager::setInstanceVisible(int instanceId, bool visible) {
    if (isValidInstanceId(instanceId)) {
        instances[instanceId].visible = visible;
    }
}

void Model3DManager::renderAll(bool wireframe) {
    for (size_t i = 0; i < instances.size(); i++) {
        renderInstance(i, wireframe);
    }
}

void Model3DManager::renderInstance(int instanceId, bool wireframe) {
    if (!isValidInstanceId(instanceId)) return;
    
    const Model3DInstance& instance = instances[instanceId];
    if (!instance.visible) return;
    
    auto it = loadedModels.find(instance.modelName);
    if (it == loadedModels.end()) return;
    
    glPushMatrix();
    applyTransform(instance);
    it->second.render(wireframe);
    glPopMatrix();
}

void Model3DManager::renderModel(const std::string& modelName, bool wireframe) {
    auto it = loadedModels.find(modelName);
    if (it != loadedModels.end()) {
        it->second.render(wireframe);
    }
}

int Model3DManager::getInstanceCount() const {
    return instances.size();
}

int Model3DManager::getLoadedModelCount() const {
    return loadedModels.size();
}

std::vector<std::string> Model3DManager::getLoadedModelNames() const {
    std::vector<std::string> names;
    for (const auto& pair : loadedModels) {
        names.push_back(pair.first);
    }
    return names;
}

Model3DInstance* Model3DManager::getInstance(int instanceId) {
    if (isValidInstanceId(instanceId)) {
        return &instances[instanceId];
    }
    return nullptr;
}

void Model3DManager::listLoadedModels() const {
    std::cout << "=== Modelos Carregados ===" << std::endl;
    if (loadedModels.empty()) {
        std::cout << "Nenhum modelo carregado." << std::endl;
    } else {
        for (const auto& pair : loadedModels) {
            const Model3D& model = pair.second;
            std::cout << "- " << pair.first << ": " << model.meshes.size() 
                      << " meshes, raio: " << model.radius << std::endl;
        }
    }
    std::cout << "=========================" << std::endl;
}

void Model3DManager::listInstances() const {
    std::cout << "=== Instâncias de Modelos ===" << std::endl;
    if (instances.empty()) {
        std::cout << "Nenhuma instância criada." << std::endl;
    } else {
        for (size_t i = 0; i < instances.size(); i++) {
            const Model3DInstance& inst = instances[i];
            std::cout << "- ID " << i << ": " << inst.modelName 
                      << " em (" << inst.position.x << ", " << inst.position.y << ", " << inst.position.z << ")"
                      << (inst.visible ? " [visível]" : " [oculto]") << std::endl;
        }
    }
    std::cout << "=============================" << std::endl;
}

void Model3DManager::applyTransform(const Model3DInstance& instance) {
    // Aplica transformações na ordem: translação, rotação, escala
    glTranslatef(instance.position.x, instance.position.y, instance.position.z);
    
    // Rotações em ordem XYZ
    if (instance.rotation.x != 0) glRotatef(instance.rotation.x, 1, 0, 0);
    if (instance.rotation.y != 0) glRotatef(instance.rotation.y, 0, 1, 0);
    if (instance.rotation.z != 0) glRotatef(instance.rotation.z, 0, 0, 1);
    
    glScalef(instance.scale.x, instance.scale.y, instance.scale.z);
}

void Model3DManager::resetTransform() {
    glLoadIdentity();
}

bool Model3DManager::isValidInstanceId(int instanceId) const {
    return instanceId >= 0 && instanceId < static_cast<int>(instances.size());
}
