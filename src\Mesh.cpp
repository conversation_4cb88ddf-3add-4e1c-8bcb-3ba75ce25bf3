#include "Mesh.h"
#include "ModelLoader.h"
#include <cmath>
#include <algorithm>
#include <iostream>

#ifndef M_PI
#define M_PI 3.14159265358979323846
#endif

Mesh::Mesh() : center(0, 0, 0), radius(1.0f) {}

Mesh::~Mesh() {
    clear();
}

void Mesh::clear() {
    vertices.clear();
    triangles.clear();
}

void Mesh::addVertex(const Vector3& position) {
    vertices.push_back(Vertex(position));
}

void Mesh::addTriangle(unsigned int a, unsigned int b, unsigned int c) {
    if (a < vertices.size() && b < vertices.size() && c < vertices.size()) {
        triangles.push_back(Triangle(a, b, c));
    }
}

void Mesh::calculateNormals() {
    for (auto& vertex : vertices) {
        vertex.normal = Vector3(0, 0, 0);
    }

    for (auto& tri : triangles) {
        calculateTriangleNormal(tri);

        vertices[tri.indices[0]].normal = vertices[tri.indices[0]].normal + tri.normal;
        vertices[tri.indices[1]].normal = vertices[tri.indices[1]].normal + tri.normal;
        vertices[tri.indices[2]].normal = vertices[tri.indices[2]].normal + tri.normal;
    }

    for (auto& vertex : vertices) {
        vertex.normal = vertex.normal.normalize();
    }
}

void Mesh::calculateTexCoords() {
    for (auto& vertex : vertices) {
        Vector3 normalized = vertex.position.normalize();
        vertex.u = 0.5f + atan2(normalized.z, normalized.x) / (2.0f * M_PI);
        vertex.v = 0.5f - asin(normalized.y) / M_PI;
    }
}

void Mesh::calculateBounds() {
    if (vertices.empty()) return;

    Vector3 min = vertices[0].position;
    Vector3 max = vertices[0].position;

    for (const auto& vertex : vertices) {
        if (vertex.position.x < min.x) min.x = vertex.position.x;
        if (vertex.position.y < min.y) min.y = vertex.position.y;
        if (vertex.position.z < min.z) min.z = vertex.position.z;
        if (vertex.position.x > max.x) max.x = vertex.position.x;
        if (vertex.position.y > max.y) max.y = vertex.position.y;
        if (vertex.position.z > max.z) max.z = vertex.position.z;
    }

    center = Vector3((min.x + max.x) * 0.5f, (min.y + max.y) * 0.5f, (min.z + max.z) * 0.5f);
    Vector3 size = max - min;
    radius = std::max({size.x, size.y, size.z}) * 0.5f;
}

void Mesh::normalize(float targetRadius) {
    calculateBounds();
    if (radius == 0) return;

    float scale = targetRadius / radius;
    for (auto& vertex : vertices) {
        vertex.position = (vertex.position - center) * scale;
    }

    center = Vector3(0, 0, 0);
    radius = targetRadius;
}

void Mesh::render(bool wireframe) {
    renderImmediate(wireframe);
}

void Mesh::renderImmediate(bool wireframe) {
    // NÃO ALTERA glPolygonMode - deixa isso para o sistema global
    // O modo wireframe/fill já foi configurado globalmente

    // FORÇA COR BRANCA PARA WIREFRAME
    if (wireframe) {
        glColor3f(1.0f, 1.0f, 1.0f);
    }

    glBegin(GL_TRIANGLES);
    for (const auto& tri : triangles) {
        for (int i = 0; i < 3; i++) {
            const Vertex& v = vertices[tri.indices[i]];
            glNormal3f(v.normal.x, v.normal.y, v.normal.z);
            glTexCoord2f(v.u, v.v);
            glVertex3f(v.position.x, v.position.y, v.position.z);
        }
    }
    glEnd();
}

void Mesh::subdivide() {
    std::vector<Triangle> newTriangles;
    std::vector<Vertex> newVertices = vertices;

    for (const auto& tri : triangles) {
        Vector3 v0 = vertices[tri.indices[0]].position;
        Vector3 v1 = vertices[tri.indices[1]].position;
        Vector3 v2 = vertices[tri.indices[2]].position;

        Vector3 m01 = (v0 + v1) * 0.5f;
        Vector3 m12 = (v1 + v2) * 0.5f;
        Vector3 m20 = (v2 + v0) * 0.5f;

        unsigned int i01 = newVertices.size();
        unsigned int i12 = i01 + 1;
        unsigned int i20 = i01 + 2;

        newVertices.push_back(Vertex(m01));
        newVertices.push_back(Vertex(m12));
        newVertices.push_back(Vertex(m20));

        newTriangles.push_back(Triangle(tri.indices[0], i01, i20));
        newTriangles.push_back(Triangle(tri.indices[1], i12, i01));
        newTriangles.push_back(Triangle(tri.indices[2], i20, i12));
        newTriangles.push_back(Triangle(i01, i12, i20));
    }

    vertices = newVertices;
    triangles = newTriangles;
}

void Mesh::smooth(float factor) {
    std::vector<Vector3> smoothedPositions(vertices.size());
    std::vector<int> neighborCount(vertices.size(), 0);

    for (auto& pos : smoothedPositions) {
        pos = Vector3(0, 0, 0);
    }

    for (const auto& tri : triangles) {
        for (int i = 0; i < 3; i++) {
            int curr = tri.indices[i];
            int next = tri.indices[(i + 1) % 3];

            smoothedPositions[curr] = smoothedPositions[curr] + vertices[next].position;
            neighborCount[curr]++;
        }
    }

    for (size_t i = 0; i < vertices.size(); i++) {
        if (neighborCount[i] > 0) {
            Vector3 avgNeighbor = smoothedPositions[i] * (1.0f / neighborCount[i]);
            vertices[i].position = vertices[i].position * (1.0f - factor) + avgNeighbor * factor;
        }
    }
}

void Mesh::displace(float amplitude, int octaves) {
    for (auto& vertex : vertices) {
        float displacement = 0;
        float freq = 1.0f;
        float amp = amplitude;

        for (int i = 0; i < octaves; i++) {
            displacement += noise3D(vertex.position.x * freq, vertex.position.y * freq, vertex.position.z * freq, i) * amp;
            freq *= 2.0f;
            amp *= 0.5f;
        }

        Vector3 normal = vertex.position.normalize();
        vertex.position = vertex.position + normal * displacement;
    }
}

Mesh Mesh::createIcosphere(int subdivisions) {
    Mesh mesh;

    const float t = (1.0f + sqrt(5.0f)) / 2.0f;

    mesh.addVertex(Vector3(-1,  t,  0));
    mesh.addVertex(Vector3( 1,  t,  0));
    mesh.addVertex(Vector3(-1, -t,  0));
    mesh.addVertex(Vector3( 1, -t,  0));
    mesh.addVertex(Vector3( 0, -1,  t));
    mesh.addVertex(Vector3( 0,  1,  t));
    mesh.addVertex(Vector3( 0, -1, -t));
    mesh.addVertex(Vector3( 0,  1, -t));
    mesh.addVertex(Vector3( t,  0, -1));
    mesh.addVertex(Vector3( t,  0,  1));
    mesh.addVertex(Vector3(-t,  0, -1));
    mesh.addVertex(Vector3(-t,  0,  1));

    mesh.addTriangle(0, 11, 5);
    mesh.addTriangle(0, 5, 1);
    mesh.addTriangle(0, 1, 7);
    mesh.addTriangle(0, 7, 10);
    mesh.addTriangle(0, 10, 11);
    mesh.addTriangle(1, 5, 9);
    mesh.addTriangle(5, 11, 4);
    mesh.addTriangle(11, 10, 2);
    mesh.addTriangle(10, 7, 6);
    mesh.addTriangle(7, 1, 8);
    mesh.addTriangle(3, 9, 4);
    mesh.addTriangle(3, 4, 2);
    mesh.addTriangle(3, 2, 6);
    mesh.addTriangle(3, 6, 8);
    mesh.addTriangle(3, 8, 9);
    mesh.addTriangle(4, 9, 5);
    mesh.addTriangle(2, 4, 11);
    mesh.addTriangle(6, 2, 10);
    mesh.addTriangle(8, 6, 7);
    mesh.addTriangle(9, 8, 1);

    for (auto& vertex : mesh.vertices) {
        vertex.position = mesh.getVertexOnUnitSphere(vertex.position);
    }

    for (int i = 0; i < subdivisions; i++) {
        mesh.subdivide();
        for (auto& vertex : mesh.vertices) {
            vertex.position = mesh.getVertexOnUnitSphere(vertex.position);
        }
    }

    mesh.calculateNormals();
    mesh.calculateTexCoords();
    mesh.calculateBounds();

    return mesh;
}

void Mesh::calculateTriangleNormal(Triangle& tri) {
    Vector3 v0 = vertices[tri.indices[0]].position;
    Vector3 v1 = vertices[tri.indices[1]].position;
    Vector3 v2 = vertices[tri.indices[2]].position;

    Vector3 edge1 = v1 - v0;
    Vector3 edge2 = v2 - v0;
    tri.normal = edge1.cross(edge2).normalize();
}

float Mesh::noise3D(float x, float y, float z, int octave) {
    int n = (int)(x * 1000) + (int)(y * 2000) * 57 + (int)(z * 3000) * 131 + octave * 7919;
    n = (n << 13) ^ n;
    return (1.0f - ((n * (n * n * 15731 + 789221) + 1376312589) & 0x7fffffff) / 1073741824.0f) * 0.5f;
}

Vector3 Mesh::getVertexOnUnitSphere(const Vector3& vertex) {
    return vertex.normalize();
}

// Implementação das funções de carregamento de modelos
Mesh Mesh::loadFromFile(const std::string& filename) {
    std::cout << "Mesh::loadFromFile: Carregando " << filename << std::endl;

    if (!ModelLoader::isFormatSupported(filename)) {
        std::cout << "Mesh::loadFromFile: Formato não suportado, criando cubo" << std::endl;
        return createCube();
    }

    Model3D model = ModelLoader::loadModel(filename);

    if (model.meshes.empty()) {
        std::cout << "Mesh::loadFromFile: Modelo vazio, criando cubo" << std::endl;
        return createCube();
    }

    // Retorna a primeira mesh do modelo
    // Para modelos com múltiplas meshes, seria necessário um sistema mais complexo
    Mesh result = model.meshes[0];
    std::cout << "Mesh::loadFromFile: Carregado com " << result.vertices.size()
              << " vértices e " << result.triangles.size() << " triângulos" << std::endl;

    return result;
}

bool Mesh::isModelFormatSupported(const std::string& filename) {
    return ModelLoader::isFormatSupported(filename);
}
