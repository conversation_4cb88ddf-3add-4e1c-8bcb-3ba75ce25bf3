#ifndef MODELLOADER_H
#define MODELLOADER_H

#include "Mesh.h"
#include <string>
#include <vector>

// Estrutura para material básico
struct Material {
    Vector3 ambient;
    Vector3 diffuse;
    Vector3 specular;
    float shininess;
    std::string diffuseTexture;
    
    Material() : ambient(0.2f, 0.2f, 0.2f), 
                 diffuse(0.8f, 0.8f, 0.8f), 
                 specular(1.0f, 1.0f, 1.0f), 
                 shininess(32.0f) {}
};

// Estrutura para um modelo 3D completo
struct Model3D {
    std::vector<Mesh> meshes;
    std::vector<Material> materials;
    Vector3 boundingBoxMin;
    Vector3 boundingBoxMax;
    Vector3 center;
    float radius;
    
    Model3D();
    void calculateBounds();
    void render(bool wireframe = false);
    void normalize(float targetRadius = 1.0f);
};

class ModelLoader {
public:
    static bool isAssimpAvailable();
    static Model3D loadModel(const std::string& filename);
    static bool isFormatSupported(const std::string& filename);
    
private:
    static std::vector<std::string> getSupportedFormats();
    static std::string getFileExtension(const std::string& filename);
    static Model3D loadWithAssimp(const std::string& filename);
    static Model3D createFallbackModel();
    static Mesh processMesh(void* mesh, void* scene);
    static Material processMaterial(void* material);
};

#endif
