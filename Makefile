# Makefile para o Simulador de Navegação entre Asteroides

# Configurações do compilador
CXX = g++
CXXFLAGS = -std=c++11 -Wall -O2

# Configurações específicas por plataforma
UNAME_S := $(shell uname -s)

ifeq ($(UNAME_S),Linux)
    LIBS = -lGL -lGLU -lglut -lm
    INCLUDES =
endif

ifeq ($(UNAME_S),Darwin)  # macOS
    LIBS = -framework OpenGL -framework GLUT
    INCLUDES =
endif

# Para Windows com MinGW
ifdef WINDIR
    LIBS = -lfreeglut32 -lopengl32 -lglu32 -lwinmm
    INCLUDES = -I../include
    LIBDIRS = -L../lib
endif

# Arquivos
TARGET = asteroid_simulator
SRCDIR = src
SOURCES = $(SRCDIR)/lookat.cpp $(SRCDIR)/LODManager.cpp $(SRCDIR)/TextureManager.cpp $(SRCDIR)/GameMenu.cpp $(SRCDIR)/Skybox.cpp $(SRCDIR)/Mesh.cpp $(SRCDIR)/ProceduralAsteroid.cpp $(SRCDIR)/Sun.cpp $(SRCDIR)/Minimap.cpp $(SRCDIR)/AudioManager.cpp $(SRCDIR)/ModelLoader.cpp $(SRCDIR)/Model3DManager.cpp

# Regra principal
$(TARGET): $(SOURCES)
	$(CXX) $(CXXFLAGS) $(INCLUDES) $(LIBDIRS) -o $(TARGET) $(SOURCES) $(LIBS)

# Limpeza
clean:
	rm -f $(TARGET) $(TARGET).exe

# Execução
run: $(TARGET)
	./$(TARGET)

# Ajuda
help:
	@echo "Makefile para Simulador de Navegação entre Asteroides"
	@echo ""
	@echo "Comandos disponíveis:"
	@echo "  make          - Compila o programa"
	@echo "  make run      - Compila e executa o programa"
	@echo "  make clean    - Remove arquivos compilados"
	@echo "  make help     - Mostra esta ajuda"
	@echo ""
	@echo "Dependências necessárias:"
	@echo "  - OpenGL"
	@echo "  - GLUT/FreeGLUT"
	@echo "  - Compilador C++ com suporte a C++11"

.PHONY: clean run help
