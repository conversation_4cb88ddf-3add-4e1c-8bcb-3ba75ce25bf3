#ifndef MODEL3DMANAGER_H
#define MODEL3DMANAGER_H

#include "ModelLoader.h"
#include "Vector3.h"
#include <map>
#include <string>
#include <memory>

// Instância de um modelo 3D no mundo
struct Model3DInstance {
    std::string modelName;
    Vector3 position;
    Vector3 rotation;
    Vector3 scale;
    bool visible;
    
    Model3DInstance() : position(0, 0, 0), rotation(0, 0, 0), 
                        scale(1, 1, 1), visible(true) {}
    
    Model3DInstance(const std::string& name, const Vector3& pos) 
        : modelName(name), position(pos), rotation(0, 0, 0), 
          scale(1, 1, 1), visible(true) {}
};

class Model3DManager {
private:
    std::map<std::string, Model3D> loadedModels;
    std::vector<Model3DInstance> instances;
    std::string modelsDirectory;
    
public:
    Model3DManager();
    ~Model3DManager();
    
    // Configuração
    void setModelsDirectory(const std::string& directory);
    
    // Carregamento de modelos
    bool loadModel(const std::string& name, const std::string& filename);
    bool isModelLoaded(const std::string& name) const;
    void unloadModel(const std::string& name);
    void unloadAllModels();
    
    // Gerenciamento de instâncias
    int createInstance(const std::string& modelName, const Vector3& position);
    void removeInstance(int instanceId);
    void setInstancePosition(int instanceId, const Vector3& position);
    void setInstanceRotation(int instanceId, const Vector3& rotation);
    void setInstanceScale(int instanceId, const Vector3& scale);
    void setInstanceVisible(int instanceId, bool visible);
    
    // Renderização
    void renderAll(bool wireframe = false);
    void renderInstance(int instanceId, bool wireframe = false);
    void renderModel(const std::string& modelName, bool wireframe = false);
    
    // Informações
    int getInstanceCount() const;
    int getLoadedModelCount() const;
    std::vector<std::string> getLoadedModelNames() const;
    Model3DInstance* getInstance(int instanceId);
    
    // Utilitários
    void listLoadedModels() const;
    void listInstances() const;
    
private:
    void applyTransform(const Model3DInstance& instance);
    void resetTransform();
    bool isValidInstanceId(int instanceId) const;
};

#endif
